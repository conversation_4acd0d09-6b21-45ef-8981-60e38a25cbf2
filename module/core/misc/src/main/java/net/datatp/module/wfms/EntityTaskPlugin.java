package net.datatp.module.wfms;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.backend.Notification;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.module.wfms.entity.ITaskableEntity;
import net.datatp.module.wfms.repo.EntityTaskRepo;
import net.datatp.util.dataformat.DataSerializer;

@Slf4j
abstract public class EntityTaskPlugin<T extends ITaskableEntity> {
  @Autowired
  EntityTaskRepo taskRepo;
  
  @Transactional(readOnly = true)
  public EntityTask getById(ClientInfo client, ICompany company, Long id) {
    return taskRepo.getById(company.getId(), id);
  }

  @Transactional(readOnly = true)
  public List<EntityTask> findByEntityId(ClientInfo client, ICompany company, String entityType, Long entityId) {
    return taskRepo.findByEntity(company.getId(), entityType, entityId);
  }
 
  abstract protected T doSaveEntity(ClientInfo client, ICompany company, T entity);
  
  
  
  protected void computeEntityRef(EntityTask task, T entity) {
    String tableName = entity.getClass().getDeclaredAnnotation(Table.class).name();
    task.setEntityRefType(tableName);
    task.setEntityRefId(entity.getId());
  }
  
  protected Notification doPersist(ClientInfo client, ICompany company, T entity, EntityTask task, EntityTaskStatus status) {
    if(log.isDebugEnabled()) {
      log.debug("Persist an entity task, status = {}", status);
      log.debug("Task: \n{}",   DataSerializer.JSON.toString(task));
      log.debug("Entity: \n{}", DataSerializer.JSON.toString(entity));
    }

    boolean isNew = task.isNew();

    if(isNew) {
      entity = createEntityTask(client, company, entity, task);
    } else if(status == EntityTaskStatus.Done) {
      entity = closeEntityTask(client, company, entity, task);
    } else {
      entity = updateEntityTask(client, company, entity, task);
    }
    
    computeEntityRef(task, entity);
    
    task.setStatus(status);
    task = saveEntityTask(client, company, task);
    
    Notification notification = 
        new Notification()
        .withMessage("Save Entity Task '{{task}}' Successfully!")
        .withParam("task", task.getLabel())
        .withAttr("task",  task)
        .withAttr("entity", entity);
    return notification;
  }
  
  protected EntityTask saveEntityTask(ClientInfo client, ICompany company, EntityTask task) {
    task = taskRepo.save(client, company, task);
    return task;
  }
  
  protected T createEntityTask(ClientInfo client, ICompany company, T entity, EntityTask task) {
    EntityTaskRequest request = entity.getEntityTaskRequest();
    request.setRequestCount(request.getRequestCount() + 1);
    request.setLastWorkRequest(createLastRequestMessage(task));
    return doSaveEntity(client, company, entity);
  }
  
  protected T closeEntityTask(ClientInfo client, ICompany company, T entity, EntityTask task) {
    EntityTaskRequest request = entity.getEntityTaskRequest();
    request.setRequestCount(request.getRequestCount() - 1);
    request.setLastWorkRequest(createLastRequestMessage(task));
    return doSaveEntity(client, company, entity);
  }
  
  protected T updateEntityTask(ClientInfo client, ICompany company, T entity, EntityTask task) {
    EntityTaskRequest request = entity.getEntityTaskRequest();
    request.setLastWorkRequest(createLastRequestMessage(task));
    return doSaveEntity(client, company, entity);
  }
  
  protected String createLastRequestMessage(EntityTask task) {
    return task.getTaskRequest() + " - " + task.getStatus();
  }
}
