
package net.datatp.module.wfms.repo;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.wfms.entity.EntityTask;

public interface EntityTaskRepo extends DataTPRepository<EntityTask, Serializable> {
  @Query("SELECT w FROM EntityTask w WHERE w.companyId = :companyId AND w.id = :id")
  EntityTask getById(@Param("companyId") Long companyId, @Param("id") Long id);

  @Query(
      """
      SELECT
        t FROM EntityTask t
      WHERE
        t.companyId = :companyId AND t.entityRefType = :entityType AND t.entityRefId = :entityId
      ORDER BY t.id DESC
      """
  )
  List<EntityTask> findByEntity(
      @Param("companyId") Long companyId, @Param("entityType") String entityType,@Param("entityId") Long entityId);

  @Modifying
  @Query("UPDATE EntityTask t SET t.storageState = :storageState WHERE t.id IN :ids ")
  int updateStorageState(@Param("storageState") StorageState state, @Param("ids") List<Long> ids);
}